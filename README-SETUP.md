# Sistema de Reservas - Sonido de la Iglesia

## Configuración del Proyecto

### 1. Configuración de Supabase

1. Ve a [supabase.com](https://supabase.com) y crea una cuenta
2. Crea un nuevo proyecto
3. Ve a Settings > API para obtener tus credenciales
4. Crea un archivo `.env` en la raíz del proyecto con:

```env
VITE_SUPABASE_URL=tu_url_de_supabase
VITE_SUPABASE_ANON_KEY=tu_clave_anonima_de_supabase
```

### 2. Configuración de la Base de Datos

Ejecuta el siguiente SQL en el editor SQL de Supabase:

```sql
-- Crear tabla de reservas
CREATE TABLE reservas (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  nombre VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  telefono VARCHAR(20) NOT NULL,
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ NOT NULL,
  comentario TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Crear índices para mejorar el rendimiento
CREATE INDEX idx_reservas_start_time ON reservas(start_time);
CREATE INDEX idx_reservas_end_time ON reservas(end_time);
CREATE INDEX idx_reservas_email ON reservas(email);

-- Habilitar RLS (Row Level Security)
ALTER TABLE reservas ENABLE ROW LEVEL SECURITY;

-- Política para permitir lectura a todos
CREATE POLICY "Permitir lectura a todos" ON reservas
  FOR SELECT USING (true);

-- Política para permitir inserción a todos
CREATE POLICY "Permitir inserción a todos" ON reservas
  FOR INSERT WITH CHECK (true);

-- Política para permitir actualización a todos
CREATE POLICY "Permitir actualización a todos" ON reservas
  FOR UPDATE USING (true);

-- Política para permitir eliminación a todos
CREATE POLICY "Permitir eliminación a todos" ON reservas
  FOR DELETE USING (true);
```

### 3. Instalación y Ejecución

```bash
# Instalar dependencias
npm install

# Ejecutar en modo desarrollo
npm run dev
```

### 4. Funcionalidades

- **Calendario interactivo**: Visualiza todas las reservas en formato calendario
- **Crear reservas**: Haz clic en una fecha/hora disponible para crear una nueva reserva
- **Ver detalles**: Haz clic en una reserva existente para ver sus detalles
- **Eliminar reservas**: Desde el modal de detalles puedes eliminar una reserva
- **Validación de horarios**: Previene conflictos de horarios automáticamente
- **Responsive**: Funciona en dispositivos móviles y desktop

### 5. Horarios Disponibles

- **Días**: Lunes a Domingo
- **Horario**: 8:00 AM - 10:00 PM
- **Duración mínima**: 1 hora

### 6. Tecnologías Utilizadas

- React 19
- Vite
- Supabase (Backend)
- FullCalendar (Calendario)
- Tailwind CSS (Estilos)
- React Hook Form (Formularios)
- date-fns (Manejo de fechas)
