import { createClient } from "@supabase/supabase-js";

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Obtener reservas (opcionalmente filtradas por fecha)
export async function getReservas(date) {
  let query = supabase.from("reservas").select("*").order("start_time", { ascending: true });

  if (date) {
    const startOfDay = new Date(`${date}T00:00:00Z`);
    const endOfDay = new Date(`${date}T23:59:59Z`);
    query = query.gte("start_time", startOfDay.toISOString()).lte("end_time", endOfDay.toISOString());
  }

  const { data, error } = await query;
  if (error) throw new Error(error.message);
  return data;
}

// Crear reserva con verificación de choques
export async function crearReserva({ nombre, email, telefono, start_time, end_time, comentario }) {
  // Revisar si hay reservas que choquen
  const { data: conflictos, error: errCheck } = await supabase
    .from("reservas")
    .select("*")
    .lt("start_time", end_time)
    .gt("end_time", start_time);

  if (errCheck) throw new Error(errCheck.message);
  if (conflictos.length > 0) throw new Error("Ese horario ya está ocupado");

  // Insertar nueva reserva
  const { data, error } = await supabase
    .from("reservas")
    .insert([{ nombre, email, telefono, start_time, end_time, comentario }])
    .select()
    .single();

  if (error) throw new Error(error.message);
  return data;
}

// Eliminar reserva
export async function eliminarReserva(id) {
  const { error } = await supabase
    .from("reservas")
    .delete()
    .eq("id", id);

  if (error) throw new Error(error.message);
}

// Actualizar reserva
export async function actualizarReserva(id, updates) {
  const { data, error } = await supabase
    .from("reservas")
    .update(updates)
    .eq("id", id)
    .select()
    .single();

  if (error) throw new Error(error.message);
  return data;
}
