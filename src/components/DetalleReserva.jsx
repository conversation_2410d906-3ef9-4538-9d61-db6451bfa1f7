import { useState } from 'react';
import { eliminar<PERSON><PERSON><PERSON> } from '../lib/supabase';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

const DetalleReserva = ({ evento, onClose, onDelete }) => {
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    if (!confirm('¿Estás seguro de que quieres eliminar esta reserva?')) {
      return;
    }

    try {
      setLoading(true);
      await eliminarReserva(evento.id);
      alert('Reserva eliminada exitosamente');
      onDelete();
      onClose();
    } catch (error) {
      console.error('Error eliminando reserva:', error);
      alert('Error eliminando la reserva: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const fechaInicio = new Date(evento.start);
  const fechaFin = new Date(evento.end);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">Detalles de Reserva</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Nombre</label>
            <p className="text-gray-900">{evento.extendedProps.nombre}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Email</label>
            <p className="text-gray-900">{evento.extendedProps.email}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Teléfono</label>
            <p className="text-gray-900">{evento.extendedProps.telefono}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Fecha</label>
            <p className="text-gray-900 capitalize">
              {format(fechaInicio, "EEEE, d 'de' MMMM 'de' yyyy", { locale: es })}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Horario</label>
            <p className="text-gray-900">
              {format(fechaInicio, 'HH:mm')} - {format(fechaFin, 'HH:mm')}
            </p>
          </div>

          {evento.extendedProps.comentario && (
            <div>
              <label className="block text-sm font-medium text-gray-700">Propósito</label>
              <p className="text-gray-900">{evento.extendedProps.comentario}</p>
            </div>
          )}
        </div>

        <div className="flex gap-3 pt-6">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
          >
            Cerrar
          </button>
          <button
            onClick={handleDelete}
            disabled={loading}
            className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
          >
            {loading ? 'Eliminando...' : 'Eliminar'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DetalleReserva;
