import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { crearReserva } from '../lib/supabase';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

const FormularioReserva = ({ selectedDate, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const { register, handleSubmit, formState: { errors }, watch } = useForm();

  const startTime = watch('startTime');

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      
      const startDateTime = new Date(`${selectedDate}T${data.startTime}:00`);
      const endDateTime = new Date(`${selectedDate}T${data.endTime}:00`);
      
      // Validar que la hora de fin sea después de la de inicio
      if (endDateTime <= startDateTime) {
        alert('La hora de fin debe ser posterior a la hora de inicio');
        return;
      }
      
      // Validar duración mínima de 1 hora
      const duracionHoras = (endDateTime - startDateTime) / (1000 * 60 * 60);
      if (duracionHoras < 1) {
        alert('La reserva debe tener una duración mínima de 1 hora');
        return;
      }
      
      await crearReserva({
        nombre: data.nombre,
        email: data.email,
        telefono: data.telefono,
        start_time: startDateTime.toISOString(),
        end_time: endDateTime.toISOString(),
        comentario: data.comentario || ''
      });
      
      alert('Reserva creada exitosamente');
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error creando reserva:', error);
      alert('Error creando la reserva: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const fechaFormateada = format(new Date(selectedDate), "EEEE, d 'de' MMMM 'de' yyyy", { locale: es });

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">Nueva Reserva</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>
        
        <p className="text-gray-600 mb-4 capitalize">{fechaFormateada}</p>
        
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nombre completo *
            </label>
            <input
              type="text"
              {...register('nombre', { required: 'El nombre es requerido' })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Tu nombre completo"
            />
            {errors.nombre && (
              <p className="text-red-500 text-sm mt-1">{errors.nombre.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email *
            </label>
            <input
              type="email"
              {...register('email', { 
                required: 'El email es requerido',
                pattern: {
                  value: /^\S+@\S+$/i,
                  message: 'Email inválido'
                }
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="<EMAIL>"
            />
            {errors.email && (
              <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Teléfono *
            </label>
            <input
              type="tel"
              {...register('telefono', { required: 'El teléfono es requerido' })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="+56 9 1234 5678"
            />
            {errors.telefono && (
              <p className="text-red-500 text-sm mt-1">{errors.telefono.message}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Hora inicio *
              </label>
              <input
                type="time"
                min="08:00"
                max="21:00"
                {...register('startTime', { required: 'La hora de inicio es requerida' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {errors.startTime && (
                <p className="text-red-500 text-sm mt-1">{errors.startTime.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Hora fin *
              </label>
              <input
                type="time"
                min={startTime || "09:00"}
                max="22:00"
                {...register('endTime', { required: 'La hora de fin es requerida' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {errors.endTime && (
                <p className="text-red-500 text-sm mt-1">{errors.endTime.message}</p>
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Propósito del evento
            </label>
            <textarea
              {...register('comentario')}
              rows="3"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Describe brevemente el propósito de tu reserva (ensayo, boda, evento especial, etc.)"
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Guardando...' : 'Reservar'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default FormularioReserva;
