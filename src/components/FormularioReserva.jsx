import React, { useState } from "react";
import axios from "axios";

export default function FormularioReserva({ start, end, onReservado }) {
  const [nombre, setNombre] = useState("");
  const [email, setEmail] = useState("");
  const [telefono, setTelefono] = useState("");
  const [comentario, setComentario] = useState("");
  const [mensaje, setMensaje] = useState("");

  const handleSubmit = async e => {
    e.preventDefault();
    try {
      await axios.post("http://localhost:4000/reservas", {
        nombre, email, telefono, comentario, start_time: start, end_time: end
      });
      setMensaje("Reserva creada con éxito!");
      onReservado(); // para refrescar el calendario
    } catch (err) {
      setMensaje(err.response?.data?.error || "Error creando reserva");
    }
  };

  if (!start || !end) return <p>Selecciona un horario en el calendario</p>;

  return (
    <form onSubmit={handleSubmit}>
      <h3>Reserva de sonido</h3>
      <p>Horario: {new Date(start).toLocaleString()} - {new Date(end).toLocaleString()}</p>
      <input type="text" placeholder="Nombre" value={nombre} onChange={e => setNombre(e.target.value)} required />
      <input type="email" placeholder="Email" value={email} onChange={e => setEmail(e.target.value)} />
      <input type="text" placeholder="Teléfono" value={telefono} onChange={e => setTelefono(e.target.value)} />
      <textarea placeholder="Comentario" value={comentario} onChange={e => setComentario(e.target.value)}></textarea>
      <button type="submit">Reservar</button>
      {mensaje && <p>{mensaje}</p>}
    </form>
  );
}