import { useState } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';

const SistemaReservasDemo = () => {
  const [mostrarFormulario, setMostrarFormulario] = useState(false);
  const [fechaSeleccionada, setFechaSeleccionada] = useState(null);
  
  // Datos de ejemplo para mostrar en el calendario
  const eventosEjemplo = [
    {
      id: '1',
      title: '<PERSON>',
      start: '2024-12-20T10:00:00',
      end: '2024-12-20T12:00:00',
      backgroundColor: '#3b82f6',
    },
    {
      id: '2',
      title: '<PERSON>',
      start: '2024-12-21T16:00:00',
      end: '2024-12-21T20:00:00',
      backgroundColor: '#10b981',
    },
    {
      id: '3',
      title: '<PERSON>',
      start: '2024-12-22T14:00:00',
      end: '2024-12-22T18:00:00',
      backgroundColor: '#f59e0b',
    }
  ];

  const handleDateSelect = (selectInfo) => {
    const now = new Date();
    const selectedDate = new Date(selectInfo.start);
    
    if (selectedDate < now) {
      alert('No puedes hacer reservas en el pasado');
      return;
    }
    
    setFechaSeleccionada(selectInfo.startStr);
    setMostrarFormulario(true);
  };

  const handleEventClick = (clickInfo) => {
    alert(`Reserva: ${clickInfo.event.title}\nHorario: ${clickInfo.event.start.toLocaleString()} - ${clickInfo.event.end.toLocaleString()}`);
  };

  const cerrarFormulario = () => {
    setMostrarFormulario(false);
    setFechaSeleccionada(null);
  };

  const handleSubmitDemo = (e) => {
    e.preventDefault();
    alert('¡Reserva creada exitosamente! (Modo demo - no se guarda realmente)');
    cerrarFormulario();
  };

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f9fafb', padding: '20px' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '30px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
            <div></div>
            <div>
              <h1 style={{ fontSize: '32px', fontWeight: 'bold', color: '#1f2937', margin: '0 0 10px 0' }}>
                Sistema de Reservas - Sonido de la Iglesia
              </h1>
              <p style={{ color: '#6b7280', margin: 0 }}>
                Reserva el equipo de sonido para tus eventos y actividades
              </p>
            </div>
            <button
              style={{
                padding: '8px 16px',
                backgroundColor: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
              onClick={() => alert('Panel de administración (próximamente)')}
            >
              Administrar
            </button>
          </div>
        </div>

        {/* Instrucciones */}
        <div style={{
          backgroundColor: '#dbeafe',
          border: '1px solid #93c5fd',
          borderRadius: '8px',
          padding: '16px',
          marginBottom: '24px'
        }}>
          <h3 style={{ fontWeight: '600', color: '#1e40af', margin: '0 0 8px 0' }}>Instrucciones:</h3>
          <ul style={{ color: '#1e40af', fontSize: '14px', margin: 0, paddingLeft: '20px' }}>
            <li>Haz clic en una fecha y hora disponible para crear una nueva reserva</li>
            <li>Haz clic en una reserva existente para ver sus detalles</li>
            <li>Las reservas deben tener una duración mínima de 1 hora</li>
            <li>Horario disponible: 8:00 AM - 10:00 PM todos los días</li>
          </ul>
        </div>

        {/* Calendario */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          padding: '24px'
        }}>
          <FullCalendar
            plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
            initialView="timeGridWeek"
            headerToolbar={{
              left: 'prev,next today',
              center: 'title',
              right: 'dayGridMonth,timeGridWeek,timeGridDay'
            }}
            events={eventosEjemplo}
            selectable={true}
            selectMirror={true}
            dayMaxEvents={true}
            weekends={true}
            select={handleDateSelect}
            eventClick={handleEventClick}
            height="auto"
            slotMinTime="08:00:00"
            slotMaxTime="22:00:00"
            allDaySlot={false}
            locale="es"
            buttonText={{
              today: 'Hoy',
              month: 'Mes',
              week: 'Semana',
              day: 'Día'
            }}
            slotLabelFormat={{
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            }}
            eventTimeFormat={{
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            }}
          />
        </div>

        {/* Modal de formulario */}
        {mostrarFormulario && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 50,
            padding: '16px'
          }}>
            <div style={{
              backgroundColor: 'white',
              borderRadius: '8px',
              padding: '24px',
              width: '100%',
              maxWidth: '400px'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>Nueva Reserva</h2>
                <button
                  onClick={cerrarFormulario}
                  style={{
                    background: 'none',
                    border: 'none',
                    fontSize: '24px',
                    color: '#6b7280',
                    cursor: 'pointer'
                  }}
                >
                  ✕
                </button>
              </div>
              
              <p style={{ color: '#6b7280', marginBottom: '16px' }}>
                Fecha seleccionada: {fechaSeleccionada}
              </p>
              
              <form onSubmit={handleSubmitDemo} style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                <div>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '4px' }}>
                    Nombre completo *
                  </label>
                  <input
                    type="text"
                    required
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                    placeholder="Tu nombre completo"
                  />
                </div>

                <div>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '4px' }}>
                    Email *
                  </label>
                  <input
                    type="email"
                    required
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '4px' }}>
                    Teléfono *
                  </label>
                  <input
                    type="tel"
                    required
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px',
                      boxSizing: 'border-box'
                    }}
                    placeholder="+56 9 1234 5678"
                  />
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                  <div>
                    <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '4px' }}>
                      Hora inicio *
                    </label>
                    <input
                      type="time"
                      required
                      min="08:00"
                      max="21:00"
                      style={{
                        width: '100%',
                        padding: '8px 12px',
                        border: '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '4px' }}>
                      Hora fin *
                    </label>
                    <input
                      type="time"
                      required
                      min="09:00"
                      max="22:00"
                      style={{
                        width: '100%',
                        padding: '8px 12px',
                        border: '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px',
                        boxSizing: 'border-box'
                      }}
                    />
                  </div>
                </div>

                <div>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '4px' }}>
                    Propósito del evento
                  </label>
                  <textarea
                    rows="3"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px',
                      boxSizing: 'border-box',
                      resize: 'vertical'
                    }}
                    placeholder="Describe brevemente el propósito de tu reserva (ensayo, boda, evento especial, etc.)"
                  />
                </div>

                <div style={{ display: 'flex', gap: '12px', paddingTop: '16px' }}>
                  <button
                    type="button"
                    onClick={cerrarFormulario}
                    style={{
                      flex: 1,
                      padding: '8px 16px',
                      border: '1px solid #d1d5db',
                      backgroundColor: 'white',
                      color: '#374151',
                      borderRadius: '6px',
                      cursor: 'pointer'
                    }}
                  >
                    Cancelar
                  </button>
                  <button
                    type="submit"
                    style={{
                      flex: 1,
                      padding: '8px 16px',
                      backgroundColor: '#2563eb',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      cursor: 'pointer'
                    }}
                  >
                    Reservar (Demo)
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SistemaReservasDemo;
