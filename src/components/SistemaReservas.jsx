import { useState } from 'react';
import CalendarioReservas from './CalendarioReservas';
import FormularioReserva from './FormularioReserva';
import DetalleReserva from './DetalleReserva';
import VistaAdmin from './VistaAdmin';
import { format } from 'date-fns';

const SistemaReservas = () => {
  const [mostrarFormulario, setMostrarFormulario] = useState(false);
  const [mostrarDetalle, setMostrarDetalle] = useState(false);
  const [mostrarAdmin, setMostrarAdmin] = useState(false);
  const [fechaSeleccionada, setFechaSeleccionada] = useState(null);
  const [eventoSeleccionado, setEventoSeleccionado] = useState(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleDateSelect = (selectInfo) => {
    const fechaSeleccionada = format(selectInfo.start, 'yyyy-MM-dd');
    setFechaSeleccionada(fechaSeleccionada);
    setMostrarFormulario(true);
  };

  const handleEventClick = (evento) => {
    setEventoSeleccionado(evento);
    setMostrarDetalle(true);
  };

  const handleReservaSuccess = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleReservaDelete = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const cerrarFormulario = () => {
    setMostrarFormulario(false);
    setFechaSeleccionada(null);
  };

  const cerrarDetalle = () => {
    setMostrarDetalle(false);
    setEventoSeleccionado(null);
  };

  const cerrarAdmin = () => {
    setMostrarAdmin(false);
    setRefreshTrigger(prev => prev + 1); // Refrescar calendario al cerrar admin
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <div className="flex justify-between items-center mb-4">
            <div></div>
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">
                Sistema de Reservas - Sonido de la Iglesia
              </h1>
              <p className="text-gray-600">
                Reserva el equipo de sonido para tus eventos y actividades
              </p>
            </div>
            <button
              onClick={() => setMostrarAdmin(true)}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm"
            >
              Administrar
            </button>
          </div>
        </div>

        <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-800 mb-2">Instrucciones:</h3>
          <ul className="text-blue-700 text-sm space-y-1">
            <li>• Haz clic en una fecha y hora disponible para crear una nueva reserva</li>
            <li>• Haz clic en una reserva existente para ver sus detalles</li>
            <li>• Las reservas deben tener una duración mínima de 1 hora</li>
            <li>• Horario disponible: 8:00 AM - 10:00 PM todos los días</li>
          </ul>
        </div>

        <CalendarioReservas
          onDateSelect={handleDateSelect}
          onEventClick={handleEventClick}
          refreshTrigger={refreshTrigger}
        />

        {mostrarFormulario && (
          <FormularioReserva
            selectedDate={fechaSeleccionada}
            onClose={cerrarFormulario}
            onSuccess={handleReservaSuccess}
          />
        )}

        {mostrarDetalle && eventoSeleccionado && (
          <DetalleReserva
            evento={eventoSeleccionado}
            onClose={cerrarDetalle}
            onDelete={handleReservaDelete}
          />
        )}

        {mostrarAdmin && (
          <VistaAdmin onClose={cerrarAdmin} />
        )}
      </div>
    </div>
  );
};

export default SistemaReservas;
