import { useState, useEffect } from 'react';
import { getReservas, eliminarReserva } from '../lib/supabase';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

const VistaAdmin = ({ onClose }) => {
  const [reservas, setReservas] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filtroFecha, setFiltroFecha] = useState('');

  useEffect(() => {
    cargarReservas();
  }, []);

  const cargarReservas = async () => {
    try {
      setLoading(true);
      const data = await getReservas();
      setReservas(data);
    } catch (error) {
      console.error('Error cargando reservas:', error);
      alert('Error cargando las reservas: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleEliminar = async (id, nombre) => {
    if (!confirm(`¿Estás seguro de que quieres eliminar la reserva de ${nombre}?`)) {
      return;
    }

    try {
      await eliminarReserva(id);
      alert('Reserva eliminada exitosamente');
      cargarReservas(); // Recargar la lista
    } catch (error) {
      console.error('Error eliminando reserva:', error);
      alert('Error eliminando la reserva: ' + error.message);
    }
  };

  const reservasFiltradas = filtroFecha 
    ? reservas.filter(reserva => {
        const fechaReserva = format(new Date(reserva.start_time), 'yyyy-MM-dd');
        return fechaReserva === filtroFecha;
      })
    : reservas;

  const reservasOrdenadas = reservasFiltradas.sort((a, b) => 
    new Date(a.start_time) - new Date(b.start_time)
  );

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6">
          <div className="text-lg">Cargando reservas...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-2xl font-bold text-gray-800">Panel de Administración</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ✕
          </button>
        </div>
        
        <div className="p-6">
          <div className="mb-4 flex gap-4 items-center">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Filtrar por fecha:
              </label>
              <input
                type="date"
                value={filtroFecha}
                onChange={(e) => setFiltroFecha(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div className="flex items-end">
              <button
                onClick={() => setFiltroFecha('')}
                className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
              >
                Mostrar todas
              </button>
            </div>
          </div>

          <div className="text-sm text-gray-600 mb-4">
            Total de reservas: {reservasOrdenadas.length}
          </div>

          <div className="overflow-y-auto max-h-96">
            {reservasOrdenadas.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No hay reservas {filtroFecha ? 'para la fecha seleccionada' : 'registradas'}
              </div>
            ) : (
              <div className="space-y-3">
                {reservasOrdenadas.map((reserva) => (
                  <div key={reserva.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex flex-wrap gap-4 mb-2">
                          <div>
                            <span className="font-semibold text-gray-700">Nombre:</span>
                            <span className="ml-2">{reserva.nombre}</span>
                          </div>
                          <div>
                            <span className="font-semibold text-gray-700">Email:</span>
                            <span className="ml-2">{reserva.email}</span>
                          </div>
                          <div>
                            <span className="font-semibold text-gray-700">Teléfono:</span>
                            <span className="ml-2">{reserva.telefono}</span>
                          </div>
                        </div>
                        
                        <div className="flex flex-wrap gap-4 mb-2">
                          <div>
                            <span className="font-semibold text-gray-700">Fecha:</span>
                            <span className="ml-2 capitalize">
                              {format(new Date(reserva.start_time), "EEEE, d 'de' MMMM 'de' yyyy", { locale: es })}
                            </span>
                          </div>
                          <div>
                            <span className="font-semibold text-gray-700">Horario:</span>
                            <span className="ml-2">
                              {format(new Date(reserva.start_time), 'HH:mm')} - {format(new Date(reserva.end_time), 'HH:mm')}
                            </span>
                          </div>
                        </div>

                        {reserva.comentario && (
                          <div className="mb-2">
                            <span className="font-semibold text-gray-700">Propósito:</span>
                            <span className="ml-2">{reserva.comentario}</span>
                          </div>
                        )}

                        <div className="text-xs text-gray-500">
                          Creada: {format(new Date(reserva.created_at), "dd/MM/yyyy 'a las' HH:mm", { locale: es })}
                        </div>
                      </div>
                      
                      <button
                        onClick={() => handleEliminar(reserva.id, reserva.nombre)}
                        className="ml-4 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
                      >
                        Eliminar
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default VistaAdmin;
