import React, { useEffect, useState } from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import axios from "axios";

export default function Calendario({ onSeleccionHora }) {
  const [events, setEvents] = useState([]);

  useEffect(() => {
    fetchReservas();
  }, []);

  const fetchReservas = async () => {
    try {
      const res = await axios.get("http://localhost:4000/reservas");
      const eventos = res.data.map(r => ({
        title: r.nombre,
        start: r.start_time,
        end: r.end_time,
      }));
      setEvents(eventos);
    } catch (err) {
      console.error(err);
    }
  };

  return (
    <FullCalendar
      plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
      initialView="timeGridWeek"
      selectable={true}
      events={events}
      select={info => {
        onSeleccionHora(info.startStr, info.endStr);
      }}
    />
  );
}