import { useState, useEffect } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { getReservas } from '../lib/supabase';

const CalendarioReservas = ({ onDateSelect, onEventClick, refreshTrigger }) => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    cargarReservas();
  }, [refreshTrigger]);

  const cargarReservas = async () => {
    try {
      setLoading(true);
      const reservas = await getReservas();
      
      const eventosCalendario = reservas.map(reserva => ({
        id: reserva.id,
        title: `${reserva.nombre} - Sonido`,
        start: reserva.start_time,
        end: reserva.end_time,
        backgroundColor: '#3b82f6',
        borderColor: '#1d4ed8',
        extendedProps: {
          nombre: reserva.nombre,
          email: reserva.email,
          telefono: reserva.telefono,
          comentario: reserva.comentario
        }
      }));
      
      setEvents(eventosCalendario);
    } catch (error) {
      console.error('Error cargando reservas:', error);
      alert('Error cargando las reservas: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDateSelect = (selectInfo) => {
    const now = new Date();
    const selectedDate = new Date(selectInfo.start);
    
    // No permitir seleccionar fechas en el pasado
    if (selectedDate < now) {
      alert('No puedes hacer reservas en el pasado');
      return;
    }
    
    onDateSelect(selectInfo);
  };

  const handleEventClick = (clickInfo) => {
    onEventClick(clickInfo.event);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Cargando calendario...</div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <FullCalendar
        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
        initialView="timeGridWeek"
        headerToolbar={{
          left: 'prev,next today',
          center: 'title',
          right: 'dayGridMonth,timeGridWeek,timeGridDay'
        }}
        events={events}
        selectable={true}
        selectMirror={true}
        dayMaxEvents={true}
        weekends={true}
        select={handleDateSelect}
        eventClick={handleEventClick}
        height="auto"
        slotMinTime="08:00:00"
        slotMaxTime="22:00:00"
        allDaySlot={false}
        locale="es"
        buttonText={{
          today: 'Hoy',
          month: 'Mes',
          week: 'Semana',
          day: 'Día'
        }}
        slotLabelFormat={{
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        }}
        eventTimeFormat={{
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        }}
      />
    </div>
  );
};

export default CalendarioReservas;
